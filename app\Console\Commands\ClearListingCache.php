<?php

namespace App\Console\Commands;

use App\Services\ListingCacheService;
use Illuminate\Console\Command;

class ClearListingCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'listing:clear-cache 
                            {--user= : Clear cache for specific user ID}
                            {--category= : Clear cache for specific category ID}
                            {--warm : Warm up caches after clearing}
                            {--stats : Show cache statistics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear listing-related caches with various options';

    /**
     * The listing cache service instance.
     *
     * @var ListingCacheService
     */
    protected $cacheService;

    /**
     * Create a new command instance.
     *
     * @param ListingCacheService $cacheService
     */
    public function __construct(ListingCacheService $cacheService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🚀 Listing Cache Management Tool');
        $this->line('================================');

        // Show cache statistics if requested
        if ($this->option('stats')) {
            $this->showCacheStats();
            return 0;
        }

        // Clear cache for specific user
        if ($userId = $this->option('user')) {
            $this->clearUserCache($userId);
            return 0;
        }

        // Clear cache for specific category
        if ($categoryId = $this->option('category')) {
            $this->clearCategoryCache($categoryId);
            return 0;
        }

        // Clear all listing caches
        $this->clearAllCaches();

        // Warm up caches if requested
        if ($this->option('warm')) {
            $this->warmUpCaches();
        }

        $this->newLine();
        $this->info('✅ Cache management completed successfully!');
        
        return 0;
    }

    /**
     * Clear all listing caches
     */
    protected function clearAllCaches()
    {
        $this->info('🧹 Clearing all listing caches...');
        
        if ($this->cacheService->clearAllListingCaches()) {
            $this->info('✅ All listing caches cleared successfully');
        } else {
            $this->error('❌ Failed to clear some listing caches');
        }
    }

    /**
     * Clear cache for specific user
     */
    protected function clearUserCache($userId)
    {
        $this->info("🧹 Clearing listing caches for user: {$userId}");
        
        if ($this->cacheService->clearUserListingCaches($userId)) {
            $this->info("✅ User listing caches cleared for user: {$userId}");
        } else {
            $this->error("❌ Failed to clear user listing caches for user: {$userId}");
        }
    }

    /**
     * Clear cache for specific category
     */
    protected function clearCategoryCache($categoryId)
    {
        $this->info("🧹 Clearing listing caches for category: {$categoryId}");
        
        if ($this->cacheService->clearCategoryListingCaches($categoryId)) {
            $this->info("✅ Category listing caches cleared for category: {$categoryId}");
        } else {
            $this->error("❌ Failed to clear category listing caches for category: {$categoryId}");
        }
    }

    /**
     * Warm up caches
     */
    protected function warmUpCaches()
    {
        $this->info('🔥 Warming up listing caches...');
        
        if ($this->cacheService->warmUpCaches()) {
            $this->info('✅ Listing caches warmed up successfully');
        } else {
            $this->error('❌ Failed to warm up listing caches');
        }
    }

    /**
     * Show cache statistics
     */
    protected function showCacheStats()
    {
        $this->info('📊 Cache Statistics');
        $this->line('==================');

        $stats = $this->cacheService->getCacheStats();
        $isWorking = $this->cacheService->isCacheWorking();
        $recommendations = $this->cacheService->getRecommendedSettings();

        $this->table(
            ['Metric', 'Value'],
            [
                ['Cache Driver', $stats['driver']],
                ['Status', $stats['status']],
                ['Last Cleared', $stats['last_cleared']],
                ['Cache Working', $isWorking ? '✅ Yes' : '❌ No'],
                ['Environment', app()->environment()],
            ]
        );

        $this->newLine();
        $this->info('🎯 Recommended Settings for ' . app()->environment() . ':');
        $this->table(
            ['Setting', 'Duration (minutes)'],
            [
                ['Default Listings', $recommendations['default_listings_duration']],
                ['Search Results', $recommendations['search_results_duration']],
                ['HTML Cache', $recommendations['html_cache_duration']],
            ]
        );
    }
}
