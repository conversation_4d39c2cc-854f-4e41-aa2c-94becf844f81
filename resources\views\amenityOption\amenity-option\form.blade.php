@push('css')
    <link rel="stylesheet" href="{{ asset('plugins/components/dropify/dist/css/dropify.min.css') }}">
@endpush
<input type="hidden" name="add_another" id="add_another" value="0">

<div class="form-group row {{ $errors->has('amenity_id') ? 'has-error' : '' }}">
    <label for="amenity_id" class="col-md-12  ">{{ translate('content_management_system.amenity_category') }}</label>
    <div class="col-md-12 form_field_padding">
        <div class="select_parent">
            <select class="form-control" name="amenity_id" id="amenity_id" disabled>
                <option value="">{{ __('select_amenity') }}</option>
                @foreach ($amenities as $amenity)
                    <option value="{{ $amenity->id }}"
                        {{ ($selected_amenity->id ?? '') == $amenity->id ? 'selected' : '' }}>{{ $amenity->name }}
                    </option>
                @endforeach
            </select>
        </div>

        {!! $errors->first('amenity_id', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="form-group row {{ $errors->has('name') ? 'has-error' : '' }}">
    <div class="col-md-12">
        <label for="amenImg" class="">{{ translate('content_management_system.image') }}</label>
        <button type="button" class="trans_btn info_tooltip" data-toggle="tooltip" data-placement="top" title="{{ translate('content_management_system.upload_amenity_image_dimensition') }}">i</button>
    </div>
    <div class="col-md-6 form_field_padding">
        <input type="file" id="amenImg" name="amenImg" class="dropify" accept="image/*" data-height="150"
            data-default-file="{{ isset($amenityoption->image) ? asset('website') . '/' . $amenityoption->image : '' }}" />
    </div>
</div>


<ul class="nav nav-pills">
    <li class="active"><a data-toggle="pill" href="#english">{{ translate('content_management_system.in_eng') }}</a></li>
    <li><a data-toggle="pill" href="#spanish">{{ translate('content_management_system.in_spanish') }}</a></li>
</ul>

<div class="tab-content">
    <div id="english" class="tab-pane fade in active">
        <div class="form-group row {{ $errors->has('en.name') ? 'has-error' : '' }}">
            <label for="enname" class="col-md-12 ">{{ translate('content_management_system.name') }} </label>
            <div class="col-md-12 form_field_padding">
                <input class="form-control" name="en[name]" type="text" id="enname"
                    value="{{ old('en.name', $amenityoption?->translate('en')->name ?? '') }}"
                    placeholder="{{ translate('content_management_system.enter') }} {{ translate('content_management_system.name') }} {{ translate('content_management_system.in_eng') }}">
                {!! $errors->first('en.name', '<p class="help-block">:message</p>') !!}
            </div>
        </div>
        <div class="form-group row">
            <label for="desc_eng" class="col-md-12  ">{{ translate('content_management_system.description') }}</label>
            <div class="col-md-12 form_field_padding">
                <textarea class="form-control" name="en[description]" rows="8" id="desc_eng"
                    placeholder="{{ translate('content_management_system.enter') }} {{ translate('content_management_system.description') }} {{ translate('content_management_system.in_eng') }}">{{ old('en.description', $amenityoption?->translate('en')->description ?? '') }}</textarea>
                {{-- {!! $errors->first('name', '<p class="help-block">:message</p>') !!} --}}
            </div>
        </div>
    </div>
    <div id="spanish" class="tab-pane fade">
        <div class="form-group row {{ $errors->has('name') ? 'has-error' : '' }}">
            <label for="name_spanish" class="col-md-12 ">{{ translate('content_management_system.name') }}</label>
            <div class="col-md-12 form_field_padding">
                <input class="form-control" name="es[name]" type="text" id="name_spanish"
                    value="{{ old('es.name', $amenityoption?->translate('es')->name ?? '') }}"
                    placeholder="{{ translate('content_management_system.enter') }} {{ translate('content_management_system.name') }} {{ translate('content_management_system.in_spanish') }}">
                {{-- {!! $errors->first('name', '<p class="help-block">:message</p>') !!} --}}
            </div>
        </div>
        <div class="form-group row">
            <label for="desc_spanish" class="col-md-12  ">{{ translate('content_management_system.description') }}</label>
            <div class="col-md-12 form_field_padding">
                <textarea class="form-control" name="es[description]" rows="8" id="desc_spanish"
                    placeholder="{{ translate('content_management_system.enter') }} {{ translate('content_management_system.description') }} {{ translate('content_management_system.in_spanish') }}">{{ old('es.description', $amenityoption?->translate('es')->description ?? '') }}</textarea>
                {{-- {!! $errors->first('name', '<p class="help-block">:message</p>') !!} --}}
            </div>
        </div>
    </div>
</div>


<div class="form-group row">
    <div class="col-md-12">
        <a class="btn cancel_btn" href="{{ url('/amenityOption/amenity-option') }}">Cancel</a>
        <input class="btn btn_yellow" type="submit" value="{{ $submitButtonText ?? 'Create' }}">
        @if (!isset($amenityoption))
            <button class="btn btn_yellow btn-another"
                id="create-add-another">{{ $submitButtonText ?? translate('content_management_system.create_add_another')}}</button>
        @endif
    </div>
</div>

@push('js')
    <script src="{{ asset('plugins/components/dropify/dist/js/dropify.min.js') }}"></script>
    <script>
        $('.dropify').dropify();
    </script>
    <script>
        $(document).ready(function() {
            $('#create-add-another').click(function() {
                $('#add_another').val(1);
            });
        });


        $(document).on('click', 'input[type="submit"], #create-add-another', function (e) {
            e.preventDefault();
            let isValid = true;
            $('input[type="text"]').each(function () {
                if ($(this).val().trim() === '') {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                   html: @json(translate('content_management_system.name_field_isrequired')),
                    confirmButtonText: @json(translate('content_management_system.ok'))
                });
            } else {
                $('form').submit();
            }
        });


    </script>
@endpush
