<?php

namespace App\Observers;

use App\Listing;
use App\Services\ListingCacheService;
use Illuminate\Support\Facades\Log;

class ListingObserver
{
    /**
     * The listing cache service instance.
     *
     * @var ListingCacheService
     */
    protected $cacheService;

    /**
     * Create a new observer instance.
     *
     * @param ListingCacheService $cacheService
     */
    public function __construct(ListingCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the Listing "created" event.
     *
     * @param  \App\Listing  $listing
     * @return void
     */
    public function created(Listing $listing)
    {
        $this->clearRelevantCaches($listing, 'created');
    }

    /**
     * Handle the Listing "updated" event.
     *
     * @param  \App\Listing  $listing
     * @return void
     */
    public function updated(Listing $listing)
    {
        $this->clearRelevantCaches($listing, 'updated');
    }

    /**
     * Handle the Listing "deleted" event.
     *
     * @param  \App\Listing  $listing
     * @return void
     */
    public function deleted(Listing $listing)
    {
        $this->clearRelevantCaches($listing, 'deleted');
    }

    /**
     * Handle the Listing "restored" event.
     *
     * @param  \App\Listing  $listing
     * @return void
     */
    public function restored(Listing $listing)
    {
        $this->clearRelevantCaches($listing, 'restored');
    }

    /**
     * Handle the Listing "force deleted" event.
     *
     * @param  \App\Listing  $listing
     * @return void
     */
    public function forceDeleted(Listing $listing)
    {
        $this->clearRelevantCaches($listing, 'force_deleted');
    }

    /**
     * Clear relevant caches when listing changes
     *
     * @param  \App\Listing  $listing
     * @param  string  $action
     * @return void
     */
    protected function clearRelevantCaches(Listing $listing, string $action)
    {
        try {
            // Clear user-specific caches
            if ($listing->user_id) {
                $this->cacheService->clearUserListingCaches($listing->user_id);
            }

            // Clear category-specific caches
            if ($listing->category_id) {
                $this->cacheService->clearCategoryListingCaches($listing->category_id);
            }

            // For major changes, clear all caches
            if (in_array($action, ['created', 'deleted', 'force_deleted'])) {
                $this->cacheService->clearAllListingCaches();
            }

            Log::info("Listing caches cleared after {$action} action", [
                'listing_id' => $listing->id,
                'user_id' => $listing->user_id,
                'category_id' => $listing->category_id,
                'action' => $action
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to clear listing caches after {$action} action", [
                'listing_id' => $listing->id,
                'error' => $e->getMessage(),
                'action' => $action
            ]);
        }
    }
}
