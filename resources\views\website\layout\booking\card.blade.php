<div class="col-md-12 booking-detail pt-3 confirm_booking_card">
    <div class="row b_shadow b_radius py-3 bg_white gap-lg-0 gap-3">
        <div class="col-md-4 col_left">
            <div class="book_img_wrapper">
                <div class="swiper mySwiper2">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide h-100">
                            <div class="slide_img">
                                <img class="img-fluid w-100 object-fit-cover border_radius"
                                    src="{{ asset('website') . '/' . ($listing->thumbnail_image->url ?? '') }}"
                                    alt="{{ $listing->thumbnail_image->name ?? '' }}" loading="lazy"
                                    onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8 col_right">
            <div class="book_box">
                <h4 class="listing-title light-bold">{{ $listing->name ?? '-' }}</h4>
                <ul class="categories list-unstyled d-flex gap-2 m-0 parent-box flex-wrap pb-2" data-aos="fade">

                    {{-- ------------- tour ------------- --}}
                    @if ($listing->category_id == 1)
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/user.svg') }}" alt="" height="20px"
                                width="20px">
                            {{ $listing->detail->booking_capacity ?? 0 }} {{ translate('stepper.total_booking_capacity') }}
                        </li>
                        @if (($listing->detail->tour_day_type ?? '') == 'same_day')
                            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                <img src="{{ asset('website/images/hourglass.png') }}" alt="" height="20px"
                                    width="20px">
                                {{ translate('stepper.same_day_tour') }}
                            </li>
                        @else
                            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                <img src="{{ asset('website/images/hourglass.png') }}" alt="" height="20px"
                                    width="20px">
                                {{ $listing->tour_durations->count() }}
                                {{ translate('stepper.day_tour') }}
                            </li>
                        @endif
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/newborn-baby-icon.png') }}" alt="" height="20px"
                                width="20px">
                            @if ($listing->detail->child_allow == 'yes')
                                {{ translate('stepper.children_allowed') }}
                            @else
                                {{ translate('stepper.children_not_allowed') }}
                            @endif
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px"
                                width="20px">
                            @if ($listing->detail->pet == 'yes')
                                {{ translate('stepper.pets_allowed') }}
                            @else
                                {{ translate('stepper.pets_not_allowed') }}
                            @endif
                        </li>
                        {{-- ------------- tour end ------------- --}}

                        {{-- ------------- Boat ------------- --}}
                    @elseif ($listing->category_id == 2)
                        <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                            {{ $listing->detail->capacity ?? '0' }} {{ translate('stepper.passengers') }}
                        </li>
                        @if ($listing->detail->boat_length != 0)
                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                {{ $listing->detail->boat_length ?? '0' }} ft
                            </li>
                        @endif
                        @if ($listing->detail->basis_type == 'Daily')
                            @isset($listing->detail->check_in_time)
                                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                    {{ translate('stepper.check_in_time') }} {{ $listing->detail->check_in_time ?? '- -' }}
                                </li>
                            @endisset
                            @isset($listing->detail->check_out_time)
                                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                    {{ translate('stepper.check_out_time') }} {{ $listing->detail->check_out_time ?? '- -' }}
                                </li>
                            @endisset
                        @endif
                        @isset($listing->activity)
                            @foreach ($listing->activity as $activity)
                                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                    {{ $activity->name }}
                                </li>
                            @endforeach
                        @endisset
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px"
                                width="20px">
                            @if ($listing->detail->pet == 'yes')
                                {{ translate('stepper.pets_allowed') }}
                            @else
                                {{ translate('stepper.pets_not_allowed') }}
                            @endif
                        </li>
                        {{-- ------------- Boat end ------------- --}}

                        {{-- ------------- Car ------------- --}}
                    @elseif ($listing->category_id == 3)
                        <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                            <img src="{{ asset('website/images/car-front.svg') }}" alt="" height="20px"
                                width="20px">
                            <span> {{ $listing->detail->seats }} {{ translate('stepper.seats') }}</span>
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                            <img src="{{ asset('website/images/car-transmission.png') }}" alt="" height="20px"
                                width="20px">
                            <span>{{ $listing->detail->transmission ?? '-' }}</span>
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                            <img src="{{ asset('website/images/car-engine.png') }}" alt="" height="20px"
                                width="25px">
                            <span>{{ $listing->detail->engine_type ?? '-' }}</span>
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px">
                            @if ($listing->detail->pet == 'yes')
                                {{ translate('stepper.pets_allowed') }}
                            @else
                                {{ translate('stepper.pets_not_allowed') }}
                            @endif
                        </li>
                        {{-- ------------- Car end ------------- --}}
                        {{-- ------------- House ------------- --}}
                    @elseif ($listing->category_id == 4)
                        <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                            <img src="{{ asset('website/images/bedroom.svg') }}" alt="" height="20px"
                                width="20px">
                            {{ $listing->detail->guests ?? '0' }} {{ translate('stepper.guests') }}
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                            <img src="{{ asset('website/images/bedroom.svg') }}" alt="" height="20px"
                                width="20px">
                            {{ $listing->detail->bedrooms ?? '0' }} {{ translate('stepper.bedrooms') }}
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                            <img src="{{ asset('website/images/bedroom.svg') }}" alt="" height="20px"
                                width="20px">
                            {{ $listing->detail->bathrooms ?? '0' }} {{ translate('stepper.bathrooms') }}
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/check-in.png') }}" alt="" height="20px" width="20px">
                           {{ translate('stepper.check_in_after') }} {{ $listing->detail->check_in_time ?? '0' }} 
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/check-out.png') }}" alt="" height="20px" width="20px">
                            {{ translate('stepper.check_out_before') }} {{ $listing->detail->check_out_time ?? '0' }}
                        </li>
                        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px">
                            @if ($listing->detail->pet == 'yes')
                                {{ translate('stepper.pets_allowed') }}
                            @else
                                {{ translate('stepper.pets_not_allowed') }}
                            @endif
                        </li>
                        {{-- ------------- House End ------------- --}}
                        {{-- medical --}}
                    @elseif ($listing->category_id == 5)
                    @endif
                </ul>
                <div class="row g-0 justify-content-between trip-detail pt-3 gap-lg-0 gap-3" data-aos="fade">
                    <div class="col-lg-6">
                        <h6 class="fs-20 light-bold">{{ translate('confirm_booking.your_trip_details') }}</h6>
                        <div class="trip-box pt-2 pb-1 d-flex gap-3">

                            @if ($reserve['total_days'] == 1)
                                <div class="same_day">
                                    <h6 class="fs-16 light-bold">
                                        @if ($listing->category_id == 1)
                                            {{ translate('confirm_booking.start_end_date') }}
                                        @elseif ($listing->category_id == 2)
                                            {{ translate('confirm_booking.departure_return_date') }}
                                        @elseif ($listing->category_id == 3)
                                           {{translate('confirm_booking.pickup_dropoff_date')}}
                                        @elseif ($listing->category_id == 4)
                                            {{ translate('confirm_booking.checkin_checkout_date') }}
                                        @endif
                                    </h6>
                                    <p class="fs-14 text-black-50">
                                        {{ date(config('constant.date_format'), strtotime($reserve['check_in'])) }}
                                    </p>
                                </div>
                            @else
                                <div class="pickup">
                                    <h6 class="fs-16 light-bold">
                                        @if ($listing->category_id == 1)
                                            {{ translate('confirm_booking.start_date') }}
                                        @elseif ($listing->category_id == 2)
                                           {{translate('confirm_booking.departure_date')}}
                                        @elseif ($listing->category_id == 3)
                                            {{ translate('confirm_booking.pickup_date') }}
                                        @elseif ($listing->category_id == 4)
                                            {{ translate('confirm_booking.checkin_date') }}
                                        @endif
                                    </h6>
                                    <p class="fs-14 text-black-50 m-0">
                                        {{ date(config('constant.date_format'), strtotime($reserve['check_in'])) }}
                                    </p>
                                </div>
                                <div class="dropoff">
                                    <h6 class="fs-16 light-bold">
                                        @if ($listing->category_id == 1)
                                            {{ translate('confirm_booking.end_date') }}
                                        @elseif ($listing->category_id == 2)
                                            {{ translate('confirm_booking.return_date') }}
                                        @elseif ($listing->category_id == 3)
                                            {{ translate('confirm_booking.dropoff_date') }}
                                        @elseif ($listing->category_id == 4)
                                            {{ translate('confirm_booking.checkout_date') }}
                                        @endif
                                    </h6>
                                    <p class="fs-14 text-black-50 m-0">
                                        {{ date(config('constant.date_format'), strtotime($reserve['check_out'] ?? $reserve['check_in'])) }}
                                    </p>
                                </div>
                                {{-- <div>
                                    <h6 class="fs-16 light-bold">Check in Date</h6>
                                    <p class="fs-14 text-black-50 m-0">
                                        {{ date(config('constant.date_format'), strtotime($reserve['check_in'])) . ' - ' . date(config('constant.date_format'), strtotime($reserve['check_out'] ?? $reserve['check_in'])) }}
                                    </p>
                                </div> --}}
                            @endif
                        </div>
                        @if ($basis_type == 'Hourly')
                            <div class="trip-box d-flex pt-2 pb-1 gap-3 align-items-end">
                                <div>
                                    <h6 class="fs-16 light-bold">{{ translate('confirm_booking.slots') }}</h6>
                                    @forelse ($reserve['hour_slots'] ?? [] as $hour_slot)
                                        <p class="fs-14 text-black-50 mb-0">
                                            {{ $hour_slot }}
                                        </p>
                                    @empty
                                        {{ translate('confirm_booking.hourly_slots_not_found') }}
                                    @endforelse
                                    {{-- <p class="fs-14 text-black-50">2 Guests</p> --}}
                                </div>
                            </div>
                        @elseif ($basis_type == 'Daily')
                            @if (in_array($listing->category_id, [3]))
                                <div class="trip-box pt-2 pb-1 pb-1 d-flex gap-3">
                                    <div class="pickup">
                                        <h6 class="fs-14 light-bold">{{ translate('confirm_booking.pickup_time') }}</h6>
                                        <p class="fs-14 text-black-50">
                                            {{ $reserve['check_in_time'] ?? '--:--' }}
                                        </p>
                                    </div>
                                    <div class="dropoff">
                                        <h6 class="fs-14 light-bold">{{ translate('confirm_booking.dropoff_time') }}</h6>
                                        <p class="fs-14 text-black-50">
                                            {{ $reserve['check_out_time'] ?? '--:--' }}
                                        </p>
                                    </div>
                                </div>
                            @elseif($listing->category_id == 2)
                                @if (isset($listing->detail->check_in_time, $listing->detail->check_out_time))
                                    <div class="trip-box pt-2 pb-1 d-flex gap-3 align-items-end">
                                        <div>
                                            <h6 class="fs-16 light-bold">{{ translate('confirm_booking.departure_time') }}</h6>
                                            <p class="fs-14 text-black-50 mb-0">
                                                {{ $listing->detail->check_in_time ?? '--:--' }}
                                            </p>
                                        </div>
                                        <div>
                                            <h6 class="fs-16 light-bold">{{ translate('confirm_booking.return_time') }}</h6>
                                            <p class="fs-14 text-black-50 mb-0">
                                                {{ $listing->detail->check_out_time ?? '--:--' }}
                                            </p>
                                        </div>
                                    </div>
                                @endif
                            @elseif($listing->category_id == 4)
                                <div class="trip-box pt-2 pb-1 d-flex gap-3 align-items-end">
                                    <div>
                                        <h6 class="fs-16 light-bold">{{ translate('confirm_booking.checkin_time') }}</h6>
                                        <p class="fs-14 text-black-50 mb-0">
                                            {{ $listing->detail->check_in_time ?? '--:--' }}
                                        </p>
                                    </div>
                                    <div>
                                        <h6 class="fs-16 light-bold">{{ translate('confirm_booking.checkout_time') }}</h6>
                                        <p class="fs-14 text-black-50 mb-0">
                                            {{ $listing->detail->check_out_time ?? '--:--' }}
                                        </p>
                                    </div>
                                </div>
                            @endif
                        @elseif ($basis_type == 'Tour')
                            <div class="trip-box pt-2 pb-1 d-flex gap-3 align-items-end">
                                @if (($listing->detail->tour_day_type ?? '') == 'same_day')
                                    <div>
                                        <h6 class="fs-16 light-bold">{{ translate('confirm_booking.start_time') }}</h6>
                                        <p class="fs-14 text-black-50 mb-0">
                                            {{-- {{ $listing->detail->check_in_time ?? '--:--' }} --}}
                                            {{ $listing->detail->start_time ?? '--:--' }}
                                        </p>
                                    </div>
                                    <div>
                                        <h6 class="fs-16 light-bold">{{ translate('confirm_booking.end_time') }}</h6>
                                        <p class="fs-14 text-black-50 mb-0">
                                            {{ $listing->detail->end_time ?? '--:--' }}
                                        </p>
                                    </div>
                                @else
                                    <div>
                                        <h6 class="fs-16 light-bold">{{ translate('confirm_booking.start_time') }}</h6>
                                        <p class="fs-14 text text-black-50 mb-0">
                                            {{ $listing->tour_durations[0]->start_time ?? '--:--' }}
                                        </p>
                                    </div>
                                    <div>
                                        <h6 class="fs-16 light-bold">{{ translate('confirm_booking.end_time') }}</h6>
                                        <p class="fs-14 text text-black-50 mb-0">
                                            {{ $listing->tour_durations[0]->end_time ?? '--:--' }}
                                        </p>
                                    </div>
                                @endif
                            </div>

                        @endif
                        @if (in_array($listing->category_id, [2, 4]))
                            <div class="trip-box pt-2 pb-1 d-flex justify-content-between align-items-end">
                                <div>
                                    <h6 class="fs-16 light-bold">{{ translate('confirm_booking.guests') }}</h6>
                                    <div class="guest_wrapper d-flex gap-2 align-items-center">
                                        <button class="btn minus_btn p-0 d_none" type="button"
                                            onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                            <i class="fa fa-minus" aria-hidden="true"></i>
                                        </button>
                                        <input type="number" min="1"
                                            max="@if ($listing->category_id == 4) {{ $listing->detail->guests ?? '1' }}@else{{ $listing->detail->capacity ?? '1' }} @endif"
                                            value="{{ $reserve['guests'] ?? 1 }}"
                                            class="form-control fs-14 text-black-50 border-0 p-0 text-center"
                                            name="guests" id="capacity" readonly />
                                        <button class="btn plus_btn p-0 d_none" type="button"
                                            onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                            <i class="fa fa-plus" aria-hidden="true"></i>
                                        </button>
                                        <span class="fs-14 text-black-50">{{ translate('confirm_booking.guests') }}</span>
                                    </div>
                                    {{-- <p class="fs-14 text-black-50">2 Guests</p> --}}
                                </div>
                                <button type="button" class="edit-trip button text-decoration border-0">{{ translate('confirm_booking.edit') }}</button>
                            </div>
                        @endif
                        @if ($listing->category_id == '1')
                            <div class="row">
                                <div
                                    class="col-md-6 trip-box pt-2 pb-1 d-flex justify-content-between align-items-end">
                                    <div>
                                        <h6 class="fs-16 light-bold">{{ translate('confirm_booking.adults') }}</h6>
                                        <div class="guest_wrapper d-flex gap-2 align-items-center">
                                            <button class="btn minus_btn p-0 d_none" type="button"
                                                onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                                <i class="fa fa-minus" aria-hidden="true"></i>
                                            </button>
                                            <input type="number" min="1"
                                                max="{{ $listing->detail->booking_capacity ?? '1' }}"
                                                value="{{ $reserve['adult_number'] ?? 1 }}"
                                                class="form-control fs-14 text-black-50 border-0 p-0 text-center"
                                                name="adult_number" id="adult_number" readonly />
                                            <button class="btn plus_btn p-0 d_none" type="button"
                                                onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                                <i class="fa fa-plus" aria-hidden="true"></i>
                                            </button>
                                            <span class="fs-14 text-black-50">{{ translate('confirm_booking.adults') }}</span>
                                        </div>
                                        {{-- <p class="fs-14 text-black-50">2 Adults</p> --}}
                                    </div>
                                    <button type="button"
                                        class="edit-trip button text-decoration border-0">{{ translate('confirm_booking.edit') }}</button>
                                </div>
                                @if ($listing->detail->child_allow == 'yes')
                                    <div
                                        class="col-md-6 trip-box pt-2 pb-1 d-flex justify-content-between align-items-end">
                                        <div>
                                            <h6 class="fs-16 light-bold">{{ translate('confirm_booking.child') }}</h6>
                                            <div class="guest_wrapper d-flex gap-2 align-items-center">
                                                <button class="btn minus_btn p-0 d_none" type="button"
                                                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                                    <i class="fa fa-minus" aria-hidden="true"></i>
                                                </button>
                                                <input type="number" min="1"
                                                    max="{{ $listing->detail->booking_capacity ?? '1' }}"
                                                    value="{{ $reserve['child_number'] ?? 1 }}"
                                                    class="form-control fs-14 text-black-50 border-0 p-0 text-center"
                                                    name="child_number" id="child_number" readonly />
                                                <button class="btn plus_btn p-0 d_none" type="button"
                                                    onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                                </button>
                                                <span class="fs-14 text-black-50">{{ translate('confirm_booking.child') }}</span>
                                            </div>
                                            {{-- <p class="fs-14 text-black-50">2 Child</p> --}}
                                        </div>
                                        <button type="button"
                                            class="edit-trip button text-decoration border-0">{{ translate('confirm_booking.edit') }}</button>
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                    <div class="col-lg-5">
                        <div class="price_wraper">
                            <h6 class="fs-20 light-bold">{{ translate('confirm_booking.price_details') }}</h6>
                            <div class="price_box">
                                {{-- Daily --}}
                                @if ($basis_type == 'Hourly')
                                    <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                        <span>
                                            {{ $currency . ' ' . number_format($listing_price * $conversion_rate, 0) }}
                                            x {{ $reserve['total_hours'] }}
                                            hour{{ $reserve['total_hours'] > 1 ? 's' : '' }}</span>
                                        <span>{{ $currency }}
                                            {{ number_format($listing_price * $reserve['total_hours'] * $conversion_rate, 0) }}</span>
                                    </div>
                                @elseif ($basis_type == 'Daily')
                                    <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                        <span>
                                            {{ $currency . ' ' . number_format($listing_price * $conversion_rate, 0) }}
                                            x
                                            {{ $reserve['total_days'] }}
                                            {{ translate('confirm_booking.day') }}{{ $reserve['total_days'] > 1 ? 's' : '' }}
                                        </span>
                                        <span>{{ $currency }}
                                            {{ number_format($listing_price * $conversion_rate * $reserve['total_days'], 0) }}</span>
                                    </div>
                                @elseif ($basis_type == 'Tour')
                                    @if ($reserve['tour_type'] == 'guests')
                                        <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                            <span>
                                                {{ $currency . ' ' . number_format($reserve['adult_price'], 0) }} x
                                                {{ $reserve['adult_number'] }} {{ translate('confirm_booking.adult') }}
                                            </span>
                                            <span>{{ $currency }}
                                                {{ number_format($reserve['adult_price'] * $reserve['adult_number'], 0) }}</span>
                                        </div>
                                        @if ($listing->detail->child_allow == 'yes' && $reserve['child_number'] > 0)
                                            <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                                <span>
                                                    {{ $currency . ' ' . number_format($reserve['child_price'], 0) }} x
                                                    {{ $reserve['child_number'] }} {{ translate('confirm_booking.child') }}
                                                </span>
                                                <span>{{ $currency }}
                                                    {{ number_format($reserve['child_price'] * $reserve['child_number'], 0) }}</span>
                                            </div>
                                        @endif
                                    @else
                                        <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                            <span>
                                                {{ translate('confirm_booking.total_adult') }}
                                            </span>
                                            <span>{{ $reserve['adult_number'] ?? 1 }}</span>
                                        </div>
                                        @if ($listing->detail->child_allow == 'yes' && $reserve['child_number'] > 0)
                                            <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                                <span>
                                                    {{ translate('confirm_booking.total_child') }}
                                                </span>
                                                <span>{{ $reserve['child_number'] ?? 1 }}</span>
                                            </div>
                                        @endif
                                        <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                            <span>
                                                {{ translate('confirm_booking.private_booking') }}
                                            </span>
                                            <span>{{ $currency }}
                                                {{ number_format($reserve['private_price_base'] * $conversion_rate) }}</span>
                                        </div>
                                    @endif
                                @endif
                                {{-- Daily end --}}

                                {{-- listing price with today days end --}}

                                {{-- weekly or monthly discount --}}
                                @if (isset($weekly_monthly_discount['discount_name'], $weekly_monthly_discount['discount_percentage']))
                                    <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                        <span>{{ $weekly_monthly_discount['discount_name'] }}
                                            ({{ $weekly_monthly_discount['discount_percentage'] }}%)</span>
                                        <span class="text-success">-
                                            {{ $currency . ' ' . number_format($weekly_monthly_discount['discount_amount'] * $conversion_rate, 0) }}</span>
                                    </div>
                                @endif
                                {{-- weekly or monthly discount end --}}

                                {{-- new listing discount --}}
                                @if (isset($new_listing_discount['discount_amount'], $new_listing_discount['discount_percentage']))
                                    <div class="d-flex justify-content-between gap-1 fs-14 mb-3">
                                        <span>{{ translate('confirm_booking.new_listing_discount') }}  
                                            ({{ $new_listing_discount['discount_percentage'] }}%)</span>
                                        <span class="text-success">-
                                            {{ $currency . ' ' . number_format($new_listing_discount['discount_amount'] * $conversion_rate, 0) }}</span>
                                    </div>
                                @endif
                                {{-- new listing  discount end --}}

                                <hr class="border-bottom border-1 border-dark">
                                {{-- total amount --}}
                                <div class="d-flex justify-content-between gap-1 fs-14 light-bold">
                                    <span>{{ translate('confirm_booking.total') }} ({{ $currency }})</span>
                                    <span>
                                        {{ $currency . ' ' . number_format($sub_total * $conversion_rate, 0) }}
                                    </span>
                                </div>
                                <hr class="border-bottom border-1 border-dark">
                                <div class="d-flex justify-content-between gap-1 fs-14 light-bold">
                                    <span>{{ translate('confirm_booking.your_payment_will_be_processed_in_usd') }}</span>
                                    <span class="bold">USD
                                        {{ round($total_usd_amount, 2) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if ($listing->detail->basis_type == "Tour")
    @push('js')
        <script>
            $(document).ready(function() {
                $(document).on("click", ".save-trip", function(e) {
                    e.preventDefault();
                    $(this).removeClass("save-trip");
                    let child_number = $("#child_number").val() || 0;
                    let adult_number = $("#adult_number").val() || 0;
                    $.ajax({
                        url: "{{ route('tour_update_booking', $listing->ids) }}",
                        data: {
                            adult_number: adult_number,
                            child_number: child_number
                        },
                        type: 'GET',
                        success: function(response) {
                            if (response.status == true) {
                                window.location.reload();
                            }else{
                                Swal.fire({
                                    title: "Oops!",
                                    text: response.message,
                                    icon: "warning"
                                });
                            }

                        }
                    })
                });
            });
        </script>
    @endpush
@endif