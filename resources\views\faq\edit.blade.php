@extends('layouts.master')
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">Update FAQ for {{ $faq->helpCenter->title ?? '-' }} Category
                    </h3>
                    @can('view-' . str_slug('HelpCenter'))
                        <a class="btn btn_yellow pull-right" href="{{ route('help-center.edit', ['help_center' => $faq->help_center_id, 'page' => $page ?? 1]) }}">
                            {{-- <i class="icon-arrow-left-circle" aria-hidden="true"></i> --}}
                             Back</a>
                    @endcan
                    <div class="clearfix"></div>
                    <hr>
                    @if ($errors->any())
                        <ul class="alert alert-danger">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif
                    <div id="faq_pane">
                        <section class="det_form cmsForms">
                            <div class="row">
                                <div class="col-md-12">
                                    <form method="POST" id="faqForm" action="{{ route('faq.update', $faq?->id) }}"
                                        accept-charset="UTF-8" class="form-horizontal" enctype="multipart/form-data">
                                        {{ method_field('PATCH') }}
                                        {{ csrf_field() }}
                                        <input type="hidden" name="page" value="{{ $page ?? 1 }}">
                                        @include('faq.form')
                                    </form>
                                </div>
                                <hr>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
