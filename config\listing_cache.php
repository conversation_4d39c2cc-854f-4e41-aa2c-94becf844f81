<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Listing Cache Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for listing-related caching.
    | You can adjust cache durations based on your application's needs.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cache Durations (in minutes)
    |--------------------------------------------------------------------------
    |
    | Define how long different types of listing data should be cached.
    | Longer durations improve performance but may show stale data.
    |
    */

    'durations' => [
        // Default listings shown on homepage
        'default_listings' => env('LISTING_CACHE_DEFAULT', 30),
        
        // Search and filter results
        'search_results' => env('LISTING_CACHE_SEARCH', 15),
        
        // Individual listing searches by name
        'listing_search' => env('LISTING_CACHE_LISTING_SEARCH', 10),
        
        // Rendered HTML cache
        'html_cache' => env('LISTING_CACHE_HTML', 5),
        
        // Service-level listing cache
        'service_listings' => env('LISTING_CACHE_SERVICE', 20),
        
        // Listing detail pages
        'listing_detail' => env('LISTING_CACHE_DETAIL', 60),
        
        // Category-based listings
        'category_listings' => env('LISTING_CACHE_CATEGORY', 45),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefixes
    |--------------------------------------------------------------------------
    |
    | Prefixes used for different types of cache keys to avoid conflicts
    | and enable pattern-based cache clearing.
    |
    */

    'prefixes' => [
        'default_listings' => 'default_listings_',
        'search' => 'search_',
        'listing_search' => 'listing_search_',
        'listing_html' => 'listing_html_',
        'service_listings' => 'listings_service_',
        'listing_detail' => 'listing_detail_',
        'category_listings' => 'category_listings_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto Cache Clearing
    |--------------------------------------------------------------------------
    |
    | Configure when caches should be automatically cleared.
    |
    */

    'auto_clear' => [
        // Clear caches when listings are created/updated/deleted
        'on_listing_changes' => env('LISTING_CACHE_AUTO_CLEAR', true),
        
        // Clear user-specific caches when user data changes
        'on_user_changes' => env('LISTING_CACHE_AUTO_CLEAR_USER', true),
        
        // Clear category caches when categories change
        'on_category_changes' => env('LISTING_CACHE_AUTO_CLEAR_CATEGORY', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-Specific Settings
    |--------------------------------------------------------------------------
    |
    | Different cache durations for different environments.
    |
    */

    'environments' => [
        'production' => [
            'default_listings' => 60,  // 1 hour
            'search_results' => 30,    // 30 minutes
            'html_cache' => 15,        // 15 minutes
            'service_listings' => 45,  // 45 minutes
        ],
        
        'staging' => [
            'default_listings' => 30,  // 30 minutes
            'search_results' => 15,    // 15 minutes
            'html_cache' => 5,         // 5 minutes
            'service_listings' => 20,  // 20 minutes
        ],
        
        'local' => [
            'default_listings' => 10,  // 10 minutes
            'search_results' => 5,     // 5 minutes
            'html_cache' => 2,         // 2 minutes
            'service_listings' => 5,   // 5 minutes
        ],
        
        'testing' => [
            'default_listings' => 1,   // 1 minute
            'search_results' => 1,     // 1 minute
            'html_cache' => 1,         // 1 minute
            'service_listings' => 1,   // 1 minute
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Tags (for Redis/Memcached)
    |--------------------------------------------------------------------------
    |
    | If using Redis or Memcached, you can use tags for more efficient
    | cache invalidation.
    |
    */

    'tags' => [
        'listings' => 'listings',
        'search' => 'search',
        'users' => 'users',
        'categories' => 'categories',
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings to optimize cache performance.
    |
    */

    'performance' => [
        // Maximum number of items to cache in collections
        'max_collection_size' => env('LISTING_CACHE_MAX_COLLECTION', 1000),
        
        // Enable/disable cache compression (if supported)
        'compress' => env('LISTING_CACHE_COMPRESS', false),
        
        // Cache warming on application boot
        'warm_on_boot' => env('LISTING_CACHE_WARM_BOOT', false),
    ],

];
