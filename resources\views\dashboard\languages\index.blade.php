@extends('layouts.master')
@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/x-editable/1.5.0/bootstrap3-editable/css/bootstrap-editable.css"
        rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
    <style>
        .floating-notification {
            position: fixed;
            padding: 10px 15px;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            font-size: 14px;
            max-width: 300px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .floating-notification.success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .floating-notification.error {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .floating-notification .icon {
            margin-right: 8px;
        }

        /* Improved search container styling */
        .search-container {
            margin-bottom: 20px;
            width: 100%;
        }

        .search-container .input-group-prepend {
            position: absolute;
            right: 8px;
            z-index: 9;
            top: 30%;
        }

        .search-container .input-group {
            width: 100%;
            position: relative;
        }

        .search-container .input-group {
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #ffce32;
        }

        .search-container .form-control {
            height: 45px;
            font-size: 15px;
            border-left: none;
            border: 0;
        }

        .search-container .input-group-text {
            background-color: white;
            border-right: none;
        }

        .search-container .btn {
            background-color: white;
            border-left: none;
        }

        #translationSearch:focus {
            box-shadow: none;
            border-color: #ced4da;
        }

        .input-group-prepend .input-group-text,
        .input-group-append .btn {
            color: #6c757d;
        }
        .clear-search {
        display: inline-block;
        position: absolute;
        top: 6px;
        right: 20px;
        z-index: 3; 
        }   

        /* Make sure the search bar is responsive */
        @media (max-width: 768px) {
            .col-md-6.mx-auto {
                width: 90%;
            }
        }
    </style>
@endpush
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <h1>{{ __('Language Translations') }}</h1>
                <p class="page-subtitle">Manage translations for multiple languages across your application</p>
            </div>
        </div>
        <div class="row mb-4 mt-3">
            <div class="col-md-6 mx-auto">
                <div class="search-container">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                        <input type="text" id="translationSearch" class="form-control"
                            placeholder="Search for keys or translations...">
                        <div class="input-group-append">
                            <button id="clearSearch" class="btn btn-outline-secondary clear-search" type="button"
                                style="display: none;">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{-- <div class="page-header">
        <h1 class="page-title">
            <i class="fa fa-language"></i> {{ __("Language Translations") }}
        </h1>
        <p class="page-subtitle">Manage translations for multiple languages across your application</p>
    </div> --}}
        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loading-spinner">
            <div class="spinner"></div>
            <p>Loading translations...</p>
        </div>
        <!-- Success Message Area -->
        {{-- <div id="success-message" class="alert alert-success-custom alert-custom" style="display: none;">
            <i class="fa fa-check-circle"></i>
            <strong>Success!</strong> <span id="success-text"></span>
        </div> --}}
        <!-- Error Message Area -->
        <div id="error-message" class="alert alert-danger-custom alert-custom" style="display: none;">
            <i class="fa fa-exclamation-circle"></i>
            <strong>Error!</strong> <span id="error-text"></span>
        </div>
        <!-- Laravel Flash Messages -->
        @if (session('success'))
            <div class="alert alert-success-custom alert-custom">
                <i class="fa fa-check-circle"></i>
                <strong>Success!</strong> {{ session('success') }}
            </div>
        @endif
        @if (session('error'))
            <div class="alert alert-danger-custom alert-custom">
                <i class="fa fa-exclamation-circle"></i>
                <strong>Error!</strong> {{ session('error') }}
            </div>
        @endif
        @if ($errors->any())
            <div class="alert alert-danger-custom alert-custom">
                <i class="fa fa-exclamation-circle"></i>
                <strong>Validation Errors:</strong>
                <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        @if (isset($mergedTranslations) && count($mergedTranslations) > 0)
            @foreach ($mergedTranslations as $page => $translations)
                <div class="panel panel-default translation-panel">
                    <div class="panel-heading section-header" role="tab" id="heading-{{ $page }}">
                        <h2 class="section-title panel-title">
                            <a role="button" data-toggle="collapse" data-parent="#translation-accordion"
                                href="#collapse-{{ $page }}" aria-expanded="false"
                                aria-controls="collapse-{{ $page }}" class="collapsed">
                                <i class="fa fa-folder-open"></i> {{ ucfirst(str_replace(['_', '-'], ' ', $page)) }}
                                <small class="text-muted">({{ count($translations['en'] ?? []) }} keys)</small>
                                <i class="fa fa-chevron-down pull-right py-2"></i>
                            </a>
                        </h2>
                    </div>
                    <div id="collapse-{{ $page }}" class="panel-collapse collapse" role="tabpanel"
                        aria-labelledby="heading-{{ $page }}">
                        <div class="panel-body">
                            <div class="table-responsive">
                                <table class="table translation-table">
                                    <thead>
                                        <tr>
                                            <th class="language-header english-col">
                                                <p
                                                    class="language-heading d-flex justify-content-center align-items-center text-dark gap-1">
                                                    {{-- <span class="language-flag" style="background: linear-gradient(to bottom, #012169 33%, #fff 33%, #fff 66%, #C8102E 66%);"></span> --}}
                                                    <span class="language-flag">
                                                        <img src="{{ asset('website') }}/images/usa.png" height="15px"
                                                            width="25px">
                                                    </span>
                                                    <span>English</span>
                                                </p>
                                            </th>
                                            <th class="language-header spanish-col">
                                                <p
                                                    class="language-heading d-flex justify-content-center align-items-center text-dark gap-1">
                                                    {{-- <span class="language-flag" style="background: linear-gradient(to bottom, #AA151B 33%, #F1BF00 33%, #F1BF00 66%, #AA151B 66%);"></span> --}}
                                                    <span class="language-flag">
                                                        <img src="{{ asset('website') }}/images/spain-flag.png"
                                                            height="15px" width="25px">
                                                    </span>
                                                    <span>Spanish</span>
                                                </p>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($translations['en'] ?? [] as $key => $enValue)
                                            <tr data-key="{{ $page }}.{{ $key }}">
                                                <td class="english-col">
                                                    <a href="#"
                                                        class="editable-value editable-en {{ empty($enValue) ? 'empty-translation' : '' }}"
                                                        data-type="text" data-pk="{{ $page }}.{{ $key }}"
                                                        data-lang="en" data-page="{{ $page }}"
                                                        data-key="{{ $key }}"
                                                        data-url="{{ route('translation.update.json') }}"
                                                        data-title="Edit English Translation">
                                                        <span
                                                            class="editable-text">{{ $enValue ?: 'Click to add English translation' }}</span>
                                                    </a>
                                                </td>
                                                <td class="spanish-col">
                                                    <a href="#"
                                                        class="editable-value editable-es {{ empty($translations['es'][$key] ?? '') ? 'empty-translation' : '' }}"
                                                        data-type="text" data-pk="{{ $page }}.{{ $key }}"
                                                        data-lang="es" data-page="{{ $page }}"
                                                        data-key="{{ $key }}"
                                                        data-url="{{ route('translation.update.json') }}"
                                                        data-title="Edit Spanish Translation">
                                                        <span
                                                            class="editable-text">{{ $translations['es'][$key] ?? 'Click to add Spanish translation' }}</span>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fa fa-language"></i>
                </div>
                <h3>No Translations Found</h3>
                <p>Get started by adding your first translation key. Translation keys help organize your app's text content
                    across multiple languages.</p>
                <button type="button" class="btn btn-add-translation btn-custom" data-toggle="modal"
                    data-target="#addTranslationModal">
                    <i class="fa fa-plus"></i> Add Your First Translation
                </button>
            </div>
        @endif
    </div>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/x-editable/1.5.0/bootstrap3-editable/js/bootstrap-editable.min.js">
    </script>
    <script type="text/javascript">
        $(document).ready(function() {
            // Wrap all panels in an accordion parent
            $('.translation-panel').wrapAll(
                '<div id="translation-accordion" class="panel-group" role="tablist" aria-multiselectable="true"></div>'
                );
            // Handle panel heading clicks
            $('.translation-panel .panel-heading').on('click', function(e) {
                var $target = $(this).next('.panel-collapse');
                if ($target.hasClass('in')) {
                    $target.collapse('hide');
                    $(this).find('.fa-chevron-down').removeClass('rotate');
                } else {
                    $target.collapse('show');
                    $(this).find('.fa-chevron-down').addClass('rotate');
                }
            });
            // Expand the first panel by default
            // Uncomment this if you want the first panel to be open by default
            // $('.translation-panel:first-child .panel-collapse').collapse('show');
            // Store the last opened panel in localStorage
            $('.translation-panel .panel-collapse').on('shown.bs.collapse', function() {
                var panelId = $(this).attr('id');
                localStorage.setItem('lastOpenedTranslationPanel', panelId);
            });

            // Restore the last opened panel from localStorage
            var lastOpenedPanel = localStorage.getItem('lastOpenedTranslationPanel');
            if (lastOpenedPanel) {
                $('#' + lastOpenedPanel).collapse('show');
            }
            // Hide loading spinner
            $('#loading-spinner').hide();
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            // Initialize editable for both English and Spanish
            $('.editable-value').editable({
                type: 'text',
                mode: 'inline',
                emptytext: 'Click to add translation',
                showbuttons: true,
                ajaxOptions: {
                    type: 'POST',
                    dataType: 'json'
                },
                params: function(params) {
                    // Extract page and key from the pk value (e.g., "home.participants")
                    var pkParts = $(this).data('pk').split('.');
                    return {
                        value: params.value,
                        language: $(this).data('lang'),
                        page: pkParts[0],
                        key: pkParts[1]
                    };
                },
                success: function(response, newValue) {
                    if (response.success) {
                        // Update the display value with the value from the response
                        $(this).find('.editable-text').text(response.value);
                        $(this).removeClass('empty-translation');
                        // Show success message
                        $('#success-message').find('#success-text').text(
                            'Translation updated successfully');
                        $('#success-message').fadeIn(300).delay(5000).fadeOut(1000);
                        // Show floating notification near this element
                        showFloatingNotification($(this), 'success',
                        'Translation updated successfully');
                        return true;
                    } else {
                        return response.message || 'Error updating translation';
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error updating translation:', xhr.responseText);
                    var errorMsg = 'Error updating translation';

                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        errorMsg += ': ' + Object.values(xhr.responseJSON.errors).join(', ');
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg += ': ' + xhr.responseJSON.message;
                    } else {
                        errorMsg += ': ' + error;
                    }
                    $('#error-message').find('#error-text').text(errorMsg);
                    $('#error-message').fadeIn(300).delay(5000).fadeOut(300);
                    // Show floating notification near this element
                    showFloatingNotification($(this), 'error', errorMsg);

                    return errorMsg;
                }
            });
            // Add event handler for editable-submit
            $(document).on('click', '.editable-submit', function(e) {
                // Get the form and the editable container
                var $form = $(this).closest('form');
                var $container = $(this).closest('.editableform');
                var $editable = $container.closest('.editable-value');
                var value = $form.find('input[type="text"], textarea').val();
                // Log for debugging
                console.log('Submit clicked, value:', value);
                // Make sure the form validation passes
                if (!value || value.trim() === '') {
                    // Show validation error
                    $container.find('.editable-error-block').text('This field is required').show();
                    return false;
                }
                // Let the default handler continue
                return true;
            });
            // Handle clicking on empty translations
            $('.editable-value').on('click', function() {
                var text = $(this).find('.editable-text').text().trim();
                if (text === 'Click to add English translation' ||
                    text === 'Click to add Spanish translation' ||
                    text === 'Click to add translation') {
                    $(this).editable('setValue', '');
                }
            });
            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
            console.log('Translation manager initialized successfully');
        });
        function showFloatingNotification(element, type, message) {
            $('.floating-notification').remove();
            var icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
            var $notification = $('<div class="floating-notification ' + type + '">' +
                '<i class="fa ' + icon + ' icon"></i>' +
                message + '</div>');
            // Append to body
            $('body').append($notification);
            var elementOffset = element.offset();
            var elementHeight = element.outerHeight();
            var windowWidth = $(window).width();
            var scrollTop = $(window).scrollTop();
            var left = Math.min(elementOffset.left + element.outerWidth(), windowWidth - $notification.outerWidth() - 20);
            var top = elementOffset.top + elementHeight + 10;
            $notification.css({
                position: 'absolute',
                left: left + 'px',
                top: top + 'px',
                opacity: 0,
                zIndex: 9999
            }).animate({
                opacity: 1
            }, 300);
            // Auto-hide after 3 seconds
            setTimeout(function() {
                $notification.animate({
                    opacity: 0
                }, 500, function() {
                    $(this).remove();
                });
            }, 3000);
        }
    </script>
    <script>
        $(document).ready(function() {
            // Translation search functionality
            $('#translationSearch').on('input', function() {
                const searchTerm = $(this).val().toLowerCase().trim();

                // Show/hide clear button
                if (searchTerm.length > 0) {
                    $('#clearSearch').show();
                } else {
                    $('#clearSearch').hide();
                }

                // Reset search if empty
                if (searchTerm === '') {
                    $('.translation-panel').each(function() {
                        $(this).show();
                        $(this).find('tr').show();
                        updatePanelCount($(this));
                    });
                    return;
                }

                let totalMatches = 0;

                // Search through all panels
                $('.translation-panel').each(function() {
                    const $panel = $(this);
                    let panelMatches = 0;

                    // Search through all rows in this panel
                    $panel.find('tr').each(function() {
                        const $row = $(this);
                        const rowText = $row.text().toLowerCase();
                        const keyText = $row.data('key') ? $row.data('key').toLowerCase() :
                            '';

                        if (rowText.includes(searchTerm) || keyText.includes(searchTerm)) {
                            $row.show();
                            panelMatches++;
                            totalMatches++;
                        } else {
                            $row.hide();
                        }
                    });

                    // Show/hide panel based on matches
                    if (panelMatches > 0) {
                        $panel.show();
                        // Expand panel if it has matches
                        const panelId = $panel.find('.panel-collapse').attr('id');
                        if (!$('#' + panelId).hasClass('in')) {
                            $panel.find('.panel-title a').removeClass('collapsed');
                            $('#' + panelId).addClass('in').css('height', '');
                        }

                        // Update the panel title to show match count
                        updatePanelCount($panel, panelMatches);
                    } else {
                        $panel.hide();
                    }
                });

                // Show a message if no matches found
                if (totalMatches === 0) {
                    if ($('#no-results-message').length === 0) {
                        $('<div id="no-results-message" class="alert alert-info mt-3">' +
                                '<i class="fa fa-info-circle"></i> No translations found matching "' +
                                searchTerm + '"</div>')
                            .insertAfter('.search-container');
                    }
                } else {
                    $('#no-results-message').remove();
                }
            });

            // Clear search button
            $('#clearSearch').on('click', function() {
                $('#translationSearch').val('').trigger('input');
                $(this).hide();
            });

            // Helper function to update panel count
            function updatePanelCount($panel, matchCount) {
                const $title = $panel.find('.panel-title a');
                const pageName = $title.text().split('(')[0].trim();
                const originalCount = $panel.find('tbody tr').length;

                if (matchCount !== undefined) {
                    $title.html('<i class="fa fa-folder-open"></i> ' + pageName +
                        ' <small class="text-muted">(' + matchCount + ' of ' + originalCount +
                        ' keys)</small>' +
                        '<i class="fa fa-chevron-down pull-right py-2"></i>');
                } else {
                    $title.html('<i class="fa fa-folder-open"></i> ' + pageName +
                        ' <small class="text-muted">(' + originalCount + ' keys)</small>' +
                        '<i class="fa fa-chevron-down pull-right py-2"></i>');
                }
            }
        });
    </script>
@endpush
