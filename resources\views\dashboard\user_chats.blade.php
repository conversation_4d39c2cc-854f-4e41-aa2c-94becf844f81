@extends('layouts.master')
@push('css')
    <style>
        .user-chat-container {
            background: white;
            padding: 10px;
            border-radius: 12px
        }

        .chat-header {
            border: 1px solid #aeaeae5a;
            padding: 8px 6px;
            margin-bottom: 23px;
        }

        .chat-box {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
        }

        .message {
            display: flex;
            justify-content: flex-start;
            /* Default alignment */
            margin-bottom: 10px;
        }

        .message.to {
            justify-content: flex-end;
            /* Align sent messages to the right */
        }

        .bubble {
            background-color: #e6e6e6;
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 8px 12px;
            max-width: 75%;
        }

        .message.to .bubble {
            background-color: #ffc200;
            color: #fff;
        }

        .message .time {
            display: block;
            font-size: 12px;
            color: #888;
            margin-top: 5px;
            text-align: right;
        }

        /* Date separator */
        .date-separator {
            text-align: center;
            font-size: 12px;
            margin: 20px 0;
            color: #999;
        }

        /* Mobile adjustments */
        @media (max-width: 768px) {
            .message .bubble {
                max-width: 90%;
                /* Make bubbles wider for small screens */
            }
        }
    </style>
@endpush
@section('content')
    <div class="container-fluid">
        <h1>User's Chat</h1>
        <div class="user-chat-container">
            <div class="row">
                <div class="col-md-4">
                    <input id="searchInput" class="form-control mb-3" name="name" placeholder="Search User " type="search">
                    <div class="list-group" id="userList">
                        @forelse ($user_chats ?? [] as $user_chat)
                            @if (isset($user_chat->from_user->id) && !in_array($user_chat->from_user->id, [1, 2]))
                                <a href="{{ route('user_chats', ['user_id' => encrypt($user_chat->from_user->id), 'to' => encrypt($user_chat->to_id)]) }}"
                                    class="list-group-item">
                                    <img src="{{ asset('website') . '/' . $user_chat->from_user->avatar }}"
                                        style="border-radius:50%; margin-right:10px" width="40" alt="">
                                    {{ $user_chat->from_user->name }} <span class="glyphicon glyphicon-arrow-right"></span>
                                    {{ $user_chat->to_user->name }}
                                </a>
                            @endif
                        @empty
                            <div  style="text-align: center; margin-top: 10px;">{{ translate('dashboard_user_chats.no_data_found') }}</div>
                        @endforelse
                    </div>
                    <div id="noData" style="display: none; text-align: center; margin-top: 10px;">{{ translate('dashboard_user_chats.no_data_found') }}</div>
                </div>
                @isset($user)
                    <div class="col-md-8">
                        <div class="chat-header">
                            <h3 class="m-0">{{ $user->name }} <span class="glyphicon glyphicon-arrow-right"></span>
                                {{ $to_user->name }}</h3>
                        </div>
                        <div class="chat-box">
                            @php
                                $previousDate = null;
                            @endphp
                            @foreach ($messages as $message)
                                @php
                                    $currentDate = \Carbon\Carbon::parse($message['created_at'])->format('F j, Y');
                                @endphp
                                @if ($previousDate != $currentDate)
                                    <div class="date-separator">{{ translate('dashboard_user_chats.date.' . $currentDate) }}</div>
                                    @php
                                        $previousDate = $currentDate;
                                    @endphp
                                @endif
                                <div class="message {{ $message['from_id'] == $user->id ? 'to' : 'from' }}">
                                    <div class="bubble">
                                        {{ translate('user.name', ['name' => $message->from_user->name]) }}
                                        <br>
                                        {!! nl2br(e(translate('message.body', ['body' => $message['body']]))) !!}
                                        <span class="time">{{ translate('time.format', ['time' => $message->created_at->format('h:i A')]) }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endisset
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        $(document).ready(function() {
            // chatBox.scrollTop(chatBox[0].scrollHeight);
            $('#searchInput').on('input', function() {
                let searchValue = $(this).val().toLowerCase();
                let userListItems = $('#userList .list-group-item');
                let noData = true;
                userListItems.each(function() {
                    let userName = $(this).text().toLowerCase();
                    if (userName.includes(searchValue)) {
                        $(this).show();
                        noData = false;
                    } else {
                        $(this).hide();
                    }
                });
                if (noData) {
                    $('#noData').show();
                } else {
                    $('#noData').hide();
                }
            });
            
            // Function to scroll the chat box to the bottom
            function scrollToBottom() {
                var chatBox = $('.chat-box');
                chatBox.scrollTop(chatBox[0].scrollHeight);
            }
            // Call the scrollToBottom function when the page loads
            scrollToBottom();
        });
    </script>
@endpush
