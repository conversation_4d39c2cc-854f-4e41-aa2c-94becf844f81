@extends('layouts.master')
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">{{ translate('dashboard_help_center.edit') }} {{ $helpcenter->title ?? '' }} {{ translate('dashboard_help_center.category') }} 
                        {{-- {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }} #{{ $helpcenter->id }} --}}
                    </h3>
                    @can('view-' . str_slug('HelpCenter'))
                        <a class="btn btn_yellow pull-right" href="{{ url('/helpCenter/help-center') }}">
                            {{-- <i class="icon-arrow-left-circle" aria-hidden="true"></i>  --}}
                            {{ translate('dashboard_help_center.back') }}</a>
                    @endcan
                    <div class="clearfix"></div>
                    <hr>
                    @if ($errors->any())
                        <ul class="alert alert-danger">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                    <form method="POST" action="{{ url('/helpCenter/help-center/' . $helpcenter->id) }}"
                        accept-charset="UTF-8" class="form-horizontal" enctype="multipart/form-data">
                        {{ method_field('PATCH') }}
                        {{ csrf_field() }}
                        @include('helpCenter.help-center.form', ['submitButtonText' => translate('dashboard_help_center.update')])
                    </form>
                </div>
            </div>
            <div class="col-md-12">
                <div class="white-box">
                    <div class="col-md-12">
                        <h3 class="box-title pull-left">{{ translate('dashboard_help_center.articles') }}</h3>
                        <a href="{{ route('faq.create', [$helpcenter->slug]) }}" class="btn btn_yellow pull-right"
                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                            {{-- <i class="fa fa-pencil-square-o" aria-hidden="true"></i> --}}
                             {{ translate('dashboard_help_center.create') }}
                        </a>
                    </div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{{ translate('dashboard_help_center.title') }}</th>
                                    <th>{{ translate('dashboard_help_center.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($faqs as $item)
                                    <tr>
                                        <td>{{ ($faqs->currentPage() - 1) * $faqs->perPage() + $loop->iteration }}</td>
                                        <td><a href="{{ route('faq.edit', ['id' => $item->id, 'help_center_id' => $helpcenter->id, 'page' => request()->get('page', 1)]) }}">{{ $item->title ?? '' }}</a></td>
                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('edit-' . str_slug('HelpCenter'))
                                                        <a href="{{ route('faq.edit', ["id" => $item->id, "page" => request()->get('page', 1)]) }}"
                                                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                                                            <button class="dropdown-item">
                                                                {{ translate('dashboard_help_center.edit') }}
                                                            </button>
                                                        </a>
                                                    @endcan
                                                    @can('delete-' . str_slug('HelpCenter'))
                                                        <form method="POST" action="{{ route('faq.delete', $item->id) }}" id="delete-form-{{ $item->id }}"
                                                            accept-charset="UTF-8" style="display:inline">
                                                            {{ method_field('DELETE') }}
                                                            {{ csrf_field() }}
                                                            <button type="button" class="dropdown-item delete-btn" data-id="{{ $item->id }}"
                                                                title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}"
                                                                >
                                                                {{ translate('dashboard_help_center.delete') }}
                                                            </button>
                                                        </form>
                                                    @endcan
                                                </div>
                                            </div>
                                        </td>




                                        {{-- <td>
                                            <a href="{{ route('faq.edit', [$item->id]) }}" class="btn btn-primary btn-sm">
                                                <i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit
                                            </a>
                                            <form method="POST" action="{{ route('faq.delete', $item->id) }}"
                                                accept-charset="UTF-8" style="display:inline">
                                                {{ method_field('DELETE') }}
                                                {{ csrf_field() }}
                                                <button type="submit" class="btn btn-danger btn-sm"
                                                    title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}"
                                                    onclick="return confirm(&quot;Confirm delete?&quot;)"><i
                                                        class="fa fa-trash-o" aria-hidden="true"></i> Delete
                                                </button>
                                            </form>
                                        </td> --}}
                                    </tr>

                                @empty
                                    <tr>
                                        <td class="text-center" colspan="4">{{ translate('dashboard_help_center.no_articles_added') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $faqs->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
{{-- @push('js')
    <script>
        // var rowIdx = $('.inputRow').length

        // // Add more FAQs
        // $('#addRow').on('click', function() {
        //     $('#inputFormRow').append(`
    //         <div class="inputRow">
    //             <div class="content-between">
    //                 <h1>FAQ #${rowIdx + 1}</h1>
    //                 <button type="button" class="btn btn-danger removeRow">Remove</button>
    //             </div>
    //             <div class="form_field_padding">
    //                 <label>Title</label>
    //                 <input class="form-control" type="text" name="items[${rowIdx}][name]" placeholder="Enter item name"/>
    //             </div>
    //             <div class="form_field_padding">
    //                 <label>Description</label>
    //                 <textarea class="form-control editor" name="items[${rowIdx}][description]" placeholder="Enter description"></textarea>
    //             </div>
    //         </div>`);
        //     initializeCKEditor()
        //     rowIdx++;
        // });
        // initializeCKEditor()

        // function initializeCKEditor() {
        //     $('.editor').each(function() {
        //         const textareaId = $(this).attr('name'); // Use the 'name' attribute for dynamic initialization

        //         // Only initialize CKEditor if it is not already initialized
        //         // if (!CKEDITOR.instances[textareaId]) {
        //         var url = "{{ route('faq_image_upload', ['_token' => csrf_token()]) }}";
        //         CKEDITOR.replace(this, {
        //             allowedContent: true,
        //             extraAllowedContent: 'p h1 h2 h3 h4 h5 h6 strong em; a[!href]; ul ol li; img[!src,alt,width,height]',
        //             disallowedContent: 'script; *[on*]',
        //             removePlugins: 'paste,sourcearea,scayt,templates,about,forms,table,tabletools,tableselection,iframe,div,language',
        //             removeButtons: 'ExportPdf,NewPage,Save',
        //             filebrowserUploadUrl: url, // Your image upload URL
        //             filebrowserUploadMethod: 'form', // Upload method
        //             filebrowserUploadParams: {
        //                 type: 'image' // Ensures only image types are allowed in file dialog
        //             }

        //         });
        //         // }
        //     });
        // }
        // // Remove an FAQ row and reindex the remaining rows
        // $(document).on('click', '.removeRow', function() {
        //     var textareaName = $(this).closest('.inputRow').find('textarea').attr('name');

        //     // Check if CKEditor instance exists for the textarea and destroy it
        //     if (CKEDITOR.instances[textareaName]) {
        //         CKEDITOR.instances[textareaName].destroy(true); // Destroy the CKEditor instance
        //     }

        //     // Remove the row from the DOM
        //     // $(this).closest('.inputRow').remove();

        //     $(this).closest('.inputRow').remove();
        //     reindexRows();
        // });

        // // Function to reindex FAQs
        // function reindexRows() {
        //     $('.inputRow').each(function(index) {
        //         // Update the FAQ header number
        //         $(this).find('h1').text('FAQ #' + (index + 1));

        //         // Update the name attributes for inputs and textareas
        //         $(this).find('input, textarea').each(function() {
        //             var name = $(this).attr('name');
        //             name = name.replace(/items\[\d+\]/, 'items[' + index + ']');
        //             $(this).attr('name', name);
        //         });
        //     });

        //     // Update rowIdx to reflect the current count of rows for next addition
        //     initializeCKEditor()
        //     rowIdx = $('.inputRow').length;
        // }
    </script>
@endpush --}}
