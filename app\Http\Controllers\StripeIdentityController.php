<?php

namespace App\Http\Controllers;

use App\Booking;
use App\Card;
use App\CommonSetting;
use App\Models\UserSession;
use App\Services\SkyflowService;
use Illuminate\Http\Request;
use Stripe\Stripe;
use App\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Stripe\Identity\VerificationSession;
use Illuminate\Support\Str;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;

class StripeIdentityController extends Controller
{

    private $clientId;
    private $clientSecret;
    private $redirectUri;
    private $apiUrl = 'https://api-sb.tipalti.com/api/v1/payees';
    private $payerName = 'Luxustars'; // Replace with your actual payer name
    private $apiKey = 'WjEXzlzKcQR9mDJZxMdO/JxAZvd210j7pdUvwSxWcr3w9h7IK+eMk6H3iV0/6qjt'; // Replace with your actual API key

    public function __construct()
    {
        $this->clientId = "tipalti.thirdpartyapi.FTeCrli-8e3rUbeQTMrzBh4FxKg";
        $this->clientSecret = "aSTyuU6SY0rjOzx0EcVzOYqyW_0";
        $this->redirectUri = "http://127.0.0.1:8000/tipalti/callback";
    }

    public function initiateVerification()
    {

        $client = new Client();
        $accessToken = session('tipalti_access_token'); // Ensure you have a valid access token
        //return session('tipalti_access_token');
        $cards = (new SkyflowService)->get_all_cards();
        // Get all sessions (both active and inactive) for the user
        $user_sessions = UserSession::forUser(auth()->id())
                                    ->orderBy('is_active', 'desc')
                                    ->orderBy('login_time', 'desc')
                                    ->take(10)
                                    ->get();

        // Check if "active" sessions are actually still valid in Laravel session storage
        foreach ($user_sessions as $session) {
            if ($session->is_active) {
                // Check if the Laravel session still exists
                if (!$this->isLaravelSessionValid($session->session_id)) {
                    // Mark as inactive if Laravel session has expired
                    $session->markAsLoggedOut();
                }
            }
        }

        // Refresh the collection to get updated data
        $user_sessions = UserSession::forUser(auth()->id())
                                    ->orderBy('is_active', 'desc')
                                    ->orderBy('login_time', 'desc')
                                    ->take(10)
                                    ->get();
        return view('website.webaccount_setting', compact('cards', 'user_sessions'));
    }
    function delete_card($card_id){
        try{
            $skyflow_bearer_token = skyflow_bearer_token();
            $response = Http::withHeaders([
                'Authorization' => $skyflow_bearer_token["tokenType"] . " " . $skyflow_bearer_token["accessToken"],
                'Content-Type' => 'application/json',
            ])->delete(skyflow_vault_url() . '/v1/vaults/' . skyflow_vault_id() . '/credit_cards/' . $card_id);
            $response->json();
            return back()->with(["message" => "Card Deleted", "type" => "success", "title" => "Success"]);
        }catch(\Exception $e){
            return back()->with(["message" => $e->getMessage(), "type" => "Error", "title" => "Error"]);
            
        }
    }
    function default_card($card_id) {
        try {
            $skyflow_bearer_token = skyflow_bearer_token();
    
            // Step 1: Get all card skyflow_ids for the current user
            $cards = Http::withHeaders([
                'Authorization' => $skyflow_bearer_token["tokenType"] . " " . $skyflow_bearer_token["accessToken"],
                'Content-Type' => 'application/json',
            ])->post(skyflow_vault_url() . '/v1/vaults/' . skyflow_vault_id() . '/query', [
                'query' => "SELECT skyflow_id FROM credit_cards WHERE user_id = " . auth()->id()
            ]);
    
            $cards = $cards->json()['records'] ?? [];
    
            // Step 2: Set is_default = 0 for all cards
            foreach ($cards as $card) {
                $skyflow_id = $card['fields']['skyflow_id'];
    
                Http::withHeaders([
                    'Authorization' => $skyflow_bearer_token["tokenType"] . " " . $skyflow_bearer_token["accessToken"],
                    'Content-Type' => 'application/json',
                ])->put(skyflow_vault_url() . '/v1/vaults/' . skyflow_vault_id() . '/credit_cards/' . $skyflow_id, [
                    'record' => [
                        'fields' => [
                            'is_default' => 0,
                        ],
                    ],
                    'tokenization' => false
                ]);
            }
    
            // Step 3: Set is_default = 1 for the selected card
            $response = Http::withHeaders([
                'Authorization' => $skyflow_bearer_token["tokenType"] . " " . $skyflow_bearer_token["accessToken"],
                'Content-Type' => 'application/json',
            ])->put(skyflow_vault_url() . '/v1/vaults/' . skyflow_vault_id() . '/credit_cards/' . $card_id, [
                'record' => [
                    'fields' => [
                        'is_default' => 1,
                    ],
                ],
                'tokenization' => false
            ]);
    
            $response->json();
    
            return back()->with([
                "message" => "Card Set As Default",
                "type" => "success",
                "title" => "Success"
            ]);
        } catch (\Exception $e) {
            return back()->with([
                "message" => $e->getMessage(),
                "type" => "Error",
                "title" => "Error"
            ]);
        }
    }
    
    function kyc_link()
    {
        try {
            // Set your Stripe secret key
            Stripe::setApiKey('sk_test_51HOynCAHJVfEXtt4HaRQLJvAi2pzdCvsLrewwuy40JhjTrbbUss4lyN6SHPxwQ7yO33n93IWGBqWzrFOJJbGMYXd00yYdmHf4w');

            // Create a verification session
            $verificationSession = VerificationSession::create([
                'type' => 'document',
                'options' => [
                    'document' => [
                        'require_id_number' => true, // Use 'require_id_number' instead of 'require_id_document'
                    ],
                ],
                'metadata' => [
                    'user_id' => auth()->id(), // Include the user's unique identifier
                ],
            ]);
            // Store the verification session ID in your database or session
            return $verificationSession;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
    function get_kyc_link()
    {
        try {
            $verificationSession = $this->kyc_link();
            return api_response(true, "data found", $verificationSession->url);
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }
    public function stripeCustomPage()
    {
        $verificationSession = $this->kyc_link();
        $verificationSessionId = $verificationSession->id;
        return view('website.stripePage', compact('verificationSession'));
    }
    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $signature = $request->header('Stripe-Signature');

        // Refresh Tipalti Access Token
        $this->refreshAccessToken();
        $accessToken = session('tipalti_access_token');

        try {
            // Verify Stripe webhook signature
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                'whsec_u6mwZF2CVG3ZRomfKB29IzkSGbYdmevc'
            );

            // Handle Identity Verification Event
            if ($event->type === 'identity.verification_session.verified') {
                $verificationSessionId = $event->data->object->id;
                $status = $event->data->object->status;
                $metadata = $event->data->object->metadata;

                if (!isset($metadata["user_id"])) {
                    return response()->json(['message' => 'User ID not found in metadata'], 400);
                }

                // Fetch User from Database
                $user = User::find($metadata["user_id"]);
                if (!$user) {
                    return response()->json(['message' => 'User not found'], 404);
                }

                // Send request to create Tipalti Payee
                $client = new Client();
                $response = $client->post($this->apiUrl, [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $accessToken,
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'contactInformation' => [
                            'email' => $user->email,
                            'firstName' => $user->first_name,
                            'middleName' => null,
                            'lastName' => $user->last_name ,
                            'companyName' => $this->payerName,
                        ],
                        'entityType' => 'INDIVIDUAL',
                        'refCode' => $user->ids,
                        'preferredPayerEntityId' => (string) $user->id,
                    ],
                ]);

                $data = json_decode($response->getBody(), true);

                if (isset($data['id'])) {
                    // Update User with Payee ID & Verification Status
                    $user->identity_verified = $status;
                    $user->payee_id = $data['id'];
                    $user->save();
                    return response()->json(['message' => 'Webhook received, user updated successfully.']);
                } else {
                    return response()->json(['message' => 'Failed to create payee.'], 500);
                }
            }

            // Return a response for unhandled webhook events
            return response()->json(['message' => 'Unhandled webhook event.']);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }
    // public function handleWebhook(Request $request)
    // {
    //     // Verify the Stripe signature for security
    //     $payload = $request->getContent();
    //     $signature = $request->header('Stripe-Signature');
    //     $client = new Client();
    //     $this->refreshAccessToken();
    //     $accessToken = session('tipalti_access_token'); // Ensure you have a valid access token

    //     //try {
    //     $event = \Stripe\Webhook::constructEvent(
    //         $payload,
    //         $signature,
    //         'whsec_u6mwZF2CVG3ZRomfKB29IzkSGbYdmevc'
    //     );
    //     // Handle different event types (e.g., identity.verification_session.updated)
    //     if ($event->type === 'identity.verification_session.verified') {
    //         // Handle verification status updates
    //         $verificationSessionId = $event->data->object->id;
    //         $status = $event->data->object->status;
    //         $metadata = $event->data->object->metadata;

    //         if (isset($metadata["user_id"])) {
    //             // --
    //             //return $accessToken;
    //             //try {
    //             $response = $client->post($this->apiUrl, [
    //                 'headers' => [
    //                     'Authorization' => 'Bearer ' . $accessToken,
    //                     'Accept' => 'application/json',
    //                     'Content-Type' => 'application/json',
    //                 ],
    //                 'json' => [
    //                     'contactInformation' => [
    //                         'email' => auth()->user()->email,
    //                         'firstName' => auth()->user()->first_name,
    //                         'middleName' => auth()->user()->last_name,
    //                         'lastName' => auth()->user()->name,
    //                         //'companyName' => $request->input('companyName'),
    //                         'companyName' => $this->payerName,
    //                     ],
    //                     'entityType' =>  'INDIVIDUAL',
    //                     //'entityType' => $request->input('entityType', 'INDIVIDUAL'),
    //                     'refCode' => auth()->user()->id,
    //                     'preferredPayerEntityId' => auth()->id(),
    //                 ],
    //             ]);

    //             $data = json_decode($response->getBody(), true);

    //             // $user->payee_id = $status;
    //             // $user->save();

    //             //return $data;

    //             if (isset($data['id'])) {
    //                 // Redirect to the iFrame display route with the new payeeId
    //                 //return redirect()->route('tipalti.showIframe', ['payeeId' => $data['id']]);
    //                 $user_id = $metadata["user_id"];
    //                 $user = User::find($user_id);
    //                 $user->identity_verified = $status;
    //                 $user->payee_id = $data['id'];
    //                 $user->save();
    //             } else {
    //                 return response()->json(['message' => 'Failed to create payee.']);
    //             }
    //             // } catch (\Exception $e) {
    //             //     return back()->with('error', 'Error: ' . $e->getMessage());
    //             // }

    //             // --
    //             return response()->json(['message' => 'Webhook received and user updated.']);
    //         } else {
    //             return response()->json(['message' => 'user_id not found']);
    //         }
    //     }
    //     // Return a response for unhandled webhook events
    //     return response()->json(['message' => 'Unhandled webhook event.']);
    //     // } catch (\Exception $e) {
    //     //     // Handle signature verification failure
    //     //     // return response()->json(['error' => 'Invalid signature'], 400);
    //     //     return response()->json(['error' => $e->getMessage()], 400);
    //     // }
    // }

    public function paymentStatus(Request $request)
    {
        Stripe::setApiKey(config('constant.STRIPE_SECRET'));

        $endpoint_secret = env('whsec_t113wFsZxUfhqsPdlEZBLSy5cHEo3IiY'); // Set this in your .env file
        $payload = $request->getContent();
        $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];

        try {
            $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid webhook signature'], 400);
        }

        // Handle the event types
        switch ($event->type) {
            case 'payment_intent.succeeded':
                // Payment was successful
                $paymentIntent = $event->data->object;
                \Log::info("Payment successful: " . $paymentIntent->id);

                // Update your database or notify frontend
                $booking = new Booking();
                $booking->payment_intent_id = $paymentIntent->id;
                $booking->status = 1;
                $booking->save();
                break;

            case 'payment_intent.payment_failed':
                // Payment failed
                $paymentIntent = $event->data->object;
                \Log::info("Payment failed: " . $paymentIntent->id);

                // Update your database or notify frontend
                $booking = new Booking();
                $booking->payment_intent_id = $paymentIntent->id;
                $booking->status = 0;
                $booking->save();
                break;

            default:
                // Unexpected event type
                \Log::warning("Unhandled event type: " . $event->type);
        }

        return response()->json(['status' => 'success']);
    }
    public function refreshAccessToken()
    {
        $client = new Client();
        $refreshToken = CommonSetting::first()->refresh_token ?? '';
        // $refreshToken = session()->get('tipalti_refresh_token');
        if (!$refreshToken) {
            //return response()->json(["error" => "Refresh token is missing."], 400);
            return redirect()->route('tipalti.auth');
        }

        try {
            $response = $client->post('https://sso.sandbox.tipalti.com/connect/token', [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refreshToken,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                ],
            ]);

            $tokenData = json_decode($response->getBody(), true);

            // Store new tokens
            session()->put('tipalti_access_token', $tokenData['access_token']);
            session()->put('tipalti_refresh_token', $tokenData['refresh_token']);
            session()->put('tipalti_token_expires_in', now()->addSeconds($tokenData['expires_in']));

            return response()->json(["message" => "Access token refreshed successfully", "token" => $tokenData]);
        } catch (\Exception $e) {
            return response()->json(["error" => "Failed to refresh access token", "message" => $e->getMessage()], 400);
        }
    }

    /**
     * Check if a Laravel session is still valid/exists
     */
    private function isLaravelSessionValid($sessionId)
    {
        try {
            $sessionDriver = config('session.driver');

            switch ($sessionDriver) {
                case 'file':
                    // For file-based sessions, check if session file exists
                    $sessionPath = config('session.files') . '/' . $sessionId;
                    return file_exists($sessionPath);

                case 'database':
                    // For database sessions, check if session record exists
                    return DB::table(config('session.table', 'sessions'))
                        ->where('id', $sessionId)
                        ->exists();

                case 'redis':
                    // For Redis sessions, check if session key exists
                    $redis = app('redis');
                    $prefix = config('session.cookie') . ':';
                    return $redis->exists($prefix . $sessionId);

                default:
                    // For other drivers, assume session is valid if marked as active
                    // This is a fallback - you might want to implement specific logic
                    return true;
            }
        } catch (\Exception $e) {
            // If we can't check, assume session is invalid to be safe
            return false;
        }
    }
}
