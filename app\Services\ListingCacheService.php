<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ListingCacheService
{
    /**
     * Get cache duration for a specific type
     */
    public function getCacheDuration($type)
    {
        $environment = app()->environment();
        $envSettings = config("listing_cache.environments.{$environment}", []);

        return $envSettings[$type] ?? config("listing_cache.durations.{$type}", 30);
    }

    /**
     * Get cache key prefix for a specific type
     */
    public function getCachePrefix($type)
    {
        return config("listing_cache.prefixes.{$type}", $type . '_');
    }

    /**
     * Clear all listing-related caches
     */
    public function clearAllListingCaches()
    {
        try {
            // For file-based cache, we need to clear all cache
            // For Redis/Memcached, you could implement pattern-based clearing
            Cache::flush();

            Log::info('All listing caches cleared successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear listing caches: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear caches for a specific user
     */
    public function clearUserListingCaches($userId = null)
    {
        $userId = $userId ?? auth()->id() ?? 'guest';
        
        try {
            $keys = [
                self::DEFAULT_LISTINGS_PREFIX . $userId,
                // Add more specific user cache keys as needed
            ];

            foreach ($keys as $key) {
                Cache::forget($key);
            }

            Log::info("User listing caches cleared for user: {$userId}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to clear user listing caches for user {$userId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear caches for a specific category
     */
    public function clearCategoryListingCaches($categoryId)
    {
        try {
            // Clear service-level category caches
            $patterns = [
                self::SERVICE_LISTINGS_PREFIX . $categoryId . '_*',
                // Add more category-specific patterns as needed
            ];

            // For simplicity, clear all caches when category changes
            Cache::flush();

            Log::info("Category listing caches cleared for category: {$categoryId}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to clear category listing caches for category {$categoryId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get cache statistics (if supported by cache driver)
     */
    public function getCacheStats()
    {
        try {
            $driver = config('cache.default');
            
            return [
                'driver' => $driver,
                'status' => 'Cache system is operational',
                'last_cleared' => Cache::get('listing_cache_last_cleared', 'Never'),
            ];
        } catch (\Exception $e) {
            return [
                'driver' => config('cache.default'),
                'status' => 'Error: ' . $e->getMessage(),
                'last_cleared' => 'Unknown',
            ];
        }
    }

    /**
     * Warm up essential caches
     */
    public function warmUpCaches()
    {
        try {
            // You can implement cache warming logic here
            // For example, pre-load popular listings, categories, etc.
            
            Cache::put('listing_cache_last_cleared', now()->toDateTimeString());
            
            Log::info('Listing caches warmed up successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to warm up listing caches: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if caching is enabled and working
     */
    public function isCacheWorking()
    {
        try {
            $testKey = 'cache_test_' . time();
            $testValue = 'test_value';
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            
            return $retrieved === $testValue;
        } catch (\Exception $e) {
            Log::error('Cache test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get recommended cache settings based on environment
     */
    public function getRecommendedSettings()
    {
        $environment = app()->environment();
        
        $settings = [
            'production' => [
                'default_listings_duration' => 60, // 1 hour
                'search_results_duration' => 30,   // 30 minutes
                'html_cache_duration' => 15,       // 15 minutes
            ],
            'staging' => [
                'default_listings_duration' => 30, // 30 minutes
                'search_results_duration' => 15,   // 15 minutes
                'html_cache_duration' => 5,        // 5 minutes
            ],
            'local' => [
                'default_listings_duration' => 10, // 10 minutes
                'search_results_duration' => 5,    // 5 minutes
                'html_cache_duration' => 2,        // 2 minutes
            ]
        ];

        return $settings[$environment] ?? $settings['local'];
    }
}
